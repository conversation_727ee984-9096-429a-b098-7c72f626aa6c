{"name": "lawengaxe-server", "version": "1.0.0", "description": "LawEngaxe backend server", "main": "src/index.ts", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "dev:port": "nodemon --exec ts-node src/index.ts -- --port", "build": "tsc", "start": "node dist/index.js", "start:port": "node dist/index.js --port", "lint": "eslint . --ext .ts", "test": "jest", "seed": "ts-node src/seeders/index.ts", "seed:dev": "nodemon --exec ts-node src/seeders/index.ts", "setup-rbac": "ts-node src/scripts/setup-rbac.ts", "setup-docs": "ts-node src/scripts/setup-docs-permission.ts", "test-auth": "ts-node src/scripts/test-auth.ts", "test-errors": "ts-node src/scripts/test-error-handling.ts", "test-api-errors": "ts-node src/scripts/test-api-errors.ts", "view-logs": "ts-node src/scripts/view-logs.ts", "test-log": "ts-node src/scripts/test-log-creation.ts", "update-admin": "ts-node src/scripts/update-admin-user.ts", "migrate-to-engaxe-ids": "ts-node src/scripts/migrate-to-engaxe-ids.ts", "test-bhashini": "ts-node src/scripts/test-bhashini-api.ts", "test-bhashini-postman": "ts-node src/scripts/test-bhashi<PERSON>-postman.ts", "test-bhashini-direct": "ts-node src/scripts/test-bhashini-direct.ts", "test-bhashini-simple": "ts-node src/scripts/test-bhashini-simple.ts"}, "dependencies": {"@fastify/cookie": "^9.0.4", "@fastify/cors": "^8.3.0", "@fastify/jwt": "^7.2.0", "@fastify/rate-limit": "^8.0.3", "@fastify/static": "^6.10.2", "@fastify/swagger": "^8.8.0", "@fastify/swagger-ui": "^1.9.3", "@fastify/websocket": "^8.2.0", "@sinclair/typebox": "^0.32.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "fastify": "^4.21.0", "fastify-plugin": "^4.5.1", "jsonwebtoken": "^9.0.2", "mongoose": "^7.4.3", "redis": "^4.6.7", "typesense": "^1.5.4", "uuid": "^9.0.0", "ws": "^8.14.2", "zod": "^3.21.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}