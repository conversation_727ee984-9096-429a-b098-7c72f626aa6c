import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Language } from '@/types';
import { ArrowLeft, ArrowRight, Loader2 } from 'lucide-react';
import { metadataAPI } from '@/services/api';
import BasicInfoStep from './wizard-steps/BasicInfoStep';
import AdditionalLanguagesStep from './wizard-steps/AdditionalLanguagesStep';

interface AddVideoWizardProps {
  languages: Language[];
  categories: string[];
  onSave: (data: {
    engaxeUrl: string;
    title: string;
    description: string;
    thumbnail: string | null;
    thumbnailFile: File | null;
    category: string;
    defaultLanguage: string;
    defaultLanguageUrl: string;
    additionalLanguages: {
      id: string;
      languageCode: string;
      url: string;
    }[];
  }) => void;
  onCancel: () => void;
  isSaving?: boolean;
}

export default function AddVideoWizard({
  languages,
  categories,
  onSave,
  onCancel,
  isSaving = false
}: AddVideoWizardProps) {
  // Current step
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 2;

  // Video data
  const [engaxeUrl, setEngaxeUrl] = useState('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [thumbnail, setThumbnail] = useState<string | null>(null);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [category, setCategory] = useState('');
  const [defaultLanguage, setDefaultLanguage] = useState('en');
  const [defaultLanguageUrl, setDefaultLanguageUrl] = useState('');
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');

  // Additional languages
  const [additionalLanguages, setAdditionalLanguages] = useState<{
    id: string;
    languageCode: string;
    url: string;
  }[]>([]);

  // Loading state for fetching metadata
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);

  // Function to handle fetching video data
  const handleFetchVideo = async () => {
    if (!engaxeUrl) return;

    setIsFetching(true);

    try {
      console.log('Fetching video metadata for URL:', engaxeUrl);

      // Call the API to fetch video metadata
      const response = await metadataAPI.fetchVideoMetadata(engaxeUrl);
      console.log('Metadata API response:', response);

      // Always use the metadata, even if success is false (we'll get fallback data)
      const { metadata } = response;
      console.log('Using metadata:', metadata);

      // Set the video data
      setTitle(metadata.title || `Engaxe Video`);
      setDescription(metadata.description || '');

      // Ensure we have a valid thumbnail URL
      if (metadata.thumbnailUrl) {
        console.log('Setting thumbnail URL:', metadata.thumbnailUrl);
        setThumbnail(metadata.thumbnailUrl);
      } else {
        console.warn('No thumbnail URL in metadata, using placeholder');
        // Extract video ID from URL
        const urlParts = engaxeUrl.split('/');
        const videoId = urlParts[urlParts.length - 1] || 'unknown';
        const placeholderUrl = `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`;
        setThumbnail(placeholderUrl);
      }

      // Set category and video preview URL
      setCategory(metadata.category || 'Education');
      setVideoPreviewUrl(metadata.embedUrl || `https://engaxe.com/embed/${engaxeUrl.split('/').pop()}`);

      // Show a message if the API call wasn't successful
      if (!response.success) {
        console.warn('API returned success=false, using fallback data');
        alert('Using default video information. You can edit the details before saving.');
      }
    } catch (error) {
      console.error('Error fetching video metadata:', error);
      alert('An error occurred while fetching video metadata. Please try again later.');

      // Set default values
      const videoId = engaxeUrl.split('/').pop() || 'unknown';
      setTitle(`Engaxe Video ${videoId}`);
      setThumbnail(`https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`);
      setCategory('Education');
    } finally {
      setIsFetching(false);
    }
  };

  // Function to go to next step
  const goToNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  // Function to go to previous step
  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Function to add a new language entry
  const addLanguageEntry = () => {
    const newEntry = {
      id: `lang-${Date.now()}`,
      languageCode: '',
      url: ''
    };

    setAdditionalLanguages(prev => [...prev, newEntry]);
  };

  // Function to update a language entry
  const updateLanguageEntry = (id: string, field: 'languageCode' | 'url', value: string) => {
    setAdditionalLanguages(prev =>
      prev.map(entry =>
        entry.id === id ? { ...entry, [field]: value } : entry
      )
    );
  };

  // Function to remove a language entry
  const removeLanguageEntry = (id: string) => {
    setAdditionalLanguages(prev => prev.filter(entry => entry.id !== id));
  };

  // Get available languages (excluding those already selected)
  const getAvailableLanguages = () => {
    const selectedLanguages = [
      defaultLanguage,
      ...additionalLanguages.map(entry => entry.languageCode)
    ].filter(Boolean);

    return languages.filter(lang => !selectedLanguages.includes(lang.code));
  };

  // Function to extract Engaxe video ID from URL
  const extractEngaxeVideoId = (url: string): string | null => {
    if (!url) return null;

    // Trim the input
    const trimmedUrl = url.trim();

    // If it's just a video ID (no slashes, dots, or protocol)
    if (!trimmedUrl.includes('/') && !trimmedUrl.includes('.') && !trimmedUrl.includes(':')) {
      // Check if it's a valid 6-7 character Engaxe ID
      if (/^[a-zA-Z0-9]{6,7}$/.test(trimmedUrl)) {
        console.log(`Input appears to be a valid 6-7 character Engaxe ID: ${trimmedUrl}`);
        return trimmedUrl;
      } else {
        console.log(`Input appears to be a direct video ID but not a valid 6-7 character Engaxe ID: ${trimmedUrl}`);
        return null;
      }
    }

    // Engaxe URL patterns - only match 6-7 character IDs
    const patterns = [
      // Format: engaxe.com/videos/[id] - only match 6-7 character IDs
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/videos\/([a-zA-Z0-9]{6,7})/i,
      // Format: engaxe.com/v/[id] - only match 6-7 character IDs
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([a-zA-Z0-9]{6,7})/i,
      // Format: engaxe.com/watch/[id] - only match 6-7 character IDs
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/watch\/([a-zA-Z0-9]{6,7})/i,
      // Format: engaxe.com/embed/[id] - only match 6-7 character IDs
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/embed\/([a-zA-Z0-9]{6,7})/i
    ];

    // Try each pattern
    for (const pattern of patterns) {
      const match = trimmedUrl.match(pattern);
      if (match && match[1]) {
        console.log(`Successfully extracted video ID: ${match[1]} using pattern: ${pattern}`);
        return match[1];
      }
    }

    // If no pattern matches, try a simple extraction as a fallback, but only accept 6-7 character IDs
    try {
      // Try to parse as URL first
      const urlObj = new URL(trimmedUrl.startsWith('http') ? trimmedUrl : `https://${trimmedUrl}`);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

      if (pathParts.length > 0) {
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart && /^[a-zA-Z0-9]{6,7}$/.test(lastPart) && !lastPart.includes('.') && !lastPart.includes('?')) {
          console.log(`Using URL path extraction, got valid 6-7 character Engaxe ID: ${lastPart}`);
          return lastPart;
        } else {
          console.log(`URL path extraction found invalid ID (not 6-7 alphanumeric characters): ${lastPart}`);
        }
      }
    } catch (error) {
      // If URL parsing fails, try simple string splitting
      const parts = trimmedUrl.split('/');
      const lastPart = parts[parts.length - 1];
      if (lastPart && /^[a-zA-Z0-9]{6,7}$/.test(lastPart) && !lastPart.includes('.') && !lastPart.includes('?')) {
        console.log(`Using fallback string extraction, got valid 6-7 character Engaxe ID: ${lastPart}`);
        return lastPart;
      } else if (lastPart) {
        console.log(`String splitting found invalid ID (not 6-7 alphanumeric characters): ${lastPart}`);
      }
    }

    console.log('Failed to extract video ID from input');
    return null;
  };

  // Handle save
  const handleSave = () => {
    if (!engaxeUrl) {
      alert("Please enter an Engaxe URL");
      return;
    }

    if (!title) {
      alert("Please enter a title");
      return;
    }

    if (!category) {
      alert("Please select a category");
      return;
    }

    if (!defaultLanguage) {
      alert("Please select a default language");
      return;
    }

    setIsLoading(true);

    // Extract video ID from URL if possible
    const videoId = extractEngaxeVideoId(engaxeUrl);
    const processedUrl = videoId || engaxeUrl;

    if (videoId) {
      console.log(`Using extracted video ID: ${videoId} (original URL: ${engaxeUrl})`);
    } else {
      console.log(`Could not extract video ID, using original URL: ${engaxeUrl}`);
    }

    // Validate that the ID is a valid 6-7 character Engaxe ID
    const isValidEngaxeId = /^[a-zA-Z0-9]{6,7}$/.test(processedUrl);
    if (!isValidEngaxeId) {
      alert(`Invalid Engaxe ID format: ${processedUrl}. Must be 6-7 alphanumeric characters.`);
      setIsLoading(false);
      return;
    }

    // Truncate description if it's too long (over 5000 characters)
    let processedDescription = description;
    if (description && description.length > 5000) {
      console.log(`Description is too long (${description.length} characters). Truncating to 5000 characters.`);
      processedDescription = description.substring(0, 4997) + '...';
    }

    // Call the onSave callback with the form data
    onSave({
      engaxeUrl: processedUrl,
      title,
      description: processedDescription,
      thumbnail,
      thumbnailFile,
      category,
      defaultLanguage,
      defaultLanguageUrl: processedUrl, // Use the processed URL/ID for default language
      additionalLanguages
    });

    setIsLoading(false);
  };

  return (
    <div className="space-y-6">
      {/* Step indicator */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 1 ? 'bg-lingstream-accent text-white' : 'bg-lingstream-hover text-lingstream-text'}`}>
            1
          </div>
          <div className="h-1 w-8 bg-lingstream-hover">
            {currentStep > 1 && <div className="h-full bg-lingstream-accent" />}
          </div>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 2 ? 'bg-lingstream-accent text-white' : 'bg-lingstream-hover text-lingstream-text'}`}>
            2
          </div>
        </div>
        <div className="text-sm text-lingstream-muted">
          Step {currentStep} of {totalSteps}
        </div>
      </div>

      {/* Step content */}
      {currentStep === 1 ? (
        <BasicInfoStep
          engaxeUrl={engaxeUrl}
          setEngaxeUrl={setEngaxeUrl}
          title={title}
          setTitle={setTitle}
          description={description}
          setDescription={setDescription}
          thumbnail={thumbnail}
          setThumbnail={setThumbnail}
          setThumbnailFile={setThumbnailFile}
          category={category}
          setCategory={setCategory}
          categories={categories}
          defaultLanguage={defaultLanguage}
          setDefaultLanguage={setDefaultLanguage}
          languages={languages}
          videoPreviewUrl={videoPreviewUrl}
          handleFetchVideo={handleFetchVideo}
          isFetching={isFetching}
        />
      ) : (
        <AdditionalLanguagesStep
          additionalLanguages={additionalLanguages}
          addLanguageEntry={addLanguageEntry}
          updateLanguageEntry={updateLanguageEntry}
          removeLanguageEntry={removeLanguageEntry}
          getAvailableLanguages={getAvailableLanguages}
          languages={languages}
        />
      )}

      {/* Navigation buttons */}
      <div className="flex justify-between pt-4 border-t">
        {currentStep === 1 ? (
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        ) : (
          <Button variant="outline" onClick={goToPreviousStep}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        )}

        {currentStep < totalSteps ? (
          <Button
            onClick={goToNextStep}
            disabled={!engaxeUrl || !title || !category || !defaultLanguage}
          >
            Next
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        ) : (
          <Button
            onClick={handleSave}
            disabled={isLoading || isSaving}
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Video'
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
