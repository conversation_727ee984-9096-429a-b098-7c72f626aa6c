import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Play, Clock } from 'lucide-react';

interface SearchVideoCardProps {
  videoId: string;
  title?: string;
  compact?: boolean; // New prop for compact chat display
}

interface VideoMetadata {
  id: string;
  title: string;
  description: string;
  duration: number;
  thumbnail: string;
  url: string;
}

export default function SearchVideoCard({ videoId, title, compact = false }: SearchVideoCardProps) {
  const [videoData, setVideoData] = useState<VideoMetadata | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Format duration from seconds to MM:SS or HH:MM:SS
  const formatDuration = (seconds: number): string => {
    if (!seconds || seconds === 0) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  };

  // Fetch video metadata
  useEffect(() => {
    const fetchVideoMetadata = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Try to get video metadata from the API
        const response = await fetch(`http://localhost:3001/api/v1/videos/metadata/${videoId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data) {
            setVideoData(data.data);
          } else {
            throw new Error('Failed to fetch video metadata');
          }
        } else {
          throw new Error(`API request failed: ${response.status}`);
        }
      } catch (error) {
        console.error('Error fetching video metadata:', error);
        setError('Failed to load video');
        
        // Fallback data
        setVideoData({
          id: videoId,
          title: title || 'Video',
          description: '',
          duration: 0,
          thumbnail: '/placeholder.svg',
          url: videoId
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (videoId) {
      fetchVideoMetadata();
    }
  }, [videoId, title]);

  if (isLoading) {
    return (
      <div className="my-4 w-full max-w-sm">
        <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 animate-pulse">
          <div className="aspect-video bg-gray-300 dark:bg-gray-700 rounded-lg mb-2"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded mb-1"></div>
          <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  if (error && !videoData) {
    return (
      <div className="my-4 w-full max-w-sm">
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3 border border-red-200 dark:border-red-800">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  if (!videoData) {
    return null;
  }

  // Compact layout for chat messages (horizontal)
  if (compact) {
    return (
      <div className="my-2 w-full max-w-md">
        <Link
          to={`/watch?id=${videoData.url || videoData.id}`}
          className="flex items-center bg-gray-50 dark:bg-gray-800 rounded-lg p-2 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow group"
        >
          {/* Thumbnail */}
          <div className="relative w-20 h-14 bg-gray-200 dark:bg-gray-700 rounded-md overflow-hidden flex-shrink-0">
            <img
              src={videoData.thumbnail || '/placeholder.svg'}
              alt={videoData.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
              onError={(e) => {
                e.currentTarget.src = '/placeholder.svg';
              }}
            />

            {/* Duration badge */}
            {videoData.duration > 0 && (
              <div className="absolute bottom-1 right-1 bg-black bg-opacity-80 text-white text-xs px-1 py-0.5 rounded text-[10px]">
                {formatDuration(videoData.duration)}
              </div>
            )}
          </div>

          {/* Video info */}
          <div className="flex-1 ml-3 min-w-0">
            <h3 className="font-medium text-sm text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
              {videoData.title}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              2 hours
            </p>
          </div>
        </Link>
      </div>
    );
  }

  // Regular layout for search results (vertical)
  return (
    <div className="my-4 w-full max-w-sm">
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
        <Link
          to={`/watch?id=${videoData.url || videoData.id}`}
          className="block group"
        >
          {/* Thumbnail */}
          <div className="relative aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden mb-3">
            <img
              src={videoData.thumbnail || '/placeholder.svg'}
              alt={videoData.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
              onError={(e) => {
                e.currentTarget.src = '/placeholder.svg';
              }}
            />

            {/* Play button overlay */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
              <div className="bg-white bg-opacity-90 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <Play className="w-4 h-4 text-gray-800 fill-current" />
              </div>
            </div>

            {/* Duration badge */}
            {videoData.duration > 0 && (
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {formatDuration(videoData.duration)}
              </div>
            )}
          </div>

          {/* Video info */}
          <div>
            <h3 className="font-medium text-sm text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors overflow-hidden"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}>
              {videoData.title}
            </h3>
            {videoData.description && (
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 overflow-hidden"
                 style={{
                   display: '-webkit-box',
                   WebkitLineClamp: 2,
                   WebkitBoxOrient: 'vertical'
                 }}>
                {videoData.description}
              </p>
            )}
          </div>
        </Link>
      </div>
    </div>
  );
}
